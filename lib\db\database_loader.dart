import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as p;
import 'package:flutter/services.dart';
import 'dart:io';

class DatabaseLoader {
  // load database from assets located in assets/db
  static Future<Database> openPrebuiltDatabase() async {
    if (kIsWeb) {
      // For web, create an in-memory database and populate it
      return await _createWebDatabase();
    } else {
      // For desktop/mobile platforms
      return await _openDesktopDatabase();
    }
  }

  static Future<Database> _openDesktopDatabase() async {
    final dbPath = await getDatabasesPath();
    final dbFile = File(p.join(dbPath, 'app.db'));
    if (kDebugMode) {
      print('Database path: ${dbFile.path}');
    }

    // Ensure the parent directory exists
    final parentDir = dbFile.parent;
    if (!await parentDir.exists()) {
      await parentDir.create(recursive: true);
    }

    if (!await dbFile.exists()) {
      // Copy the prebuilt database from assets
      final byteData = await rootBundle.load('assets/db/app.db');
      final buffer = byteData.buffer.asUint8List();
      await dbFile.writeAsBytes(buffer, flush: true);
    }

    return openDatabase(dbFile.path);
  }

  static Future<Database> _createWebDatabase() async {
    // Create an in-memory database for web
    return await openDatabase(
      ':memory:',
      version: 1,
      onCreate: (db, version) async {
        // Create the channels table structure
        await db.execute('''
          CREATE TABLE channels (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            url TEXT NOT NULL,
            category TEXT,
            logo TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        ''');

        // Insert some default channels for web demo
        await db.insert('channels', {
          'name': 'Sample Channel 1',
          'url': 'https://example.com/stream1.m3u8',
          'category': 'Entertainment',
          'logo': null,
        });

        await db.insert('channels', {
          'name': 'Sample Channel 2',
          'url': 'https://example.com/stream2.m3u8',
          'category': 'News',
          'logo': null,
        });
      },
    );
  }
}
